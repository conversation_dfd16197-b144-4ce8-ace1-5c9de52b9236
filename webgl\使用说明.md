# Unity WebGL 弹窗功能使用说明

## 🎯 功能概述

已成功为Unity WebGL界面实现了5个下拉菜单项目的弹窗功能，所有弹窗统一尺寸为1366×768像素。

## 📋 操作步骤

### 1. 打开主页面
访问：`webgl/main.html`

### 2. 打开下拉菜单
- 点击页面顶部的 **"中科院等离子极向场无功补偿SVG-B3"** 标题
- 或者点击右侧的 **"菜单"** 按钮（带有三条横线图标）

### 3. 选择功能模块
下拉菜单中包含以下5个新增功能：
- 📈 **实时曲线** - 显示实时数据曲线
- 📊 **历史记录** - 查看历史数据记录  
- 📅 **历史事件** - 浏览历史事件日志
- 🌊 **故障录波** - 故障波形分析
- ℹ️ **版本信息** - 系统版本信息

### 4. 弹窗操作
- 点击任意菜单项后，会自动打开对应的弹窗
- 弹窗尺寸：1366×768像素
- 默认加载：百度首页 (http://www.baidu.com)
- 关闭方式：
  - 点击弹窗右上角的 ❌ 按钮
  - 点击弹窗外的遮罩区域

## 🔧 现有功能更新

以下4个现有弹窗的尺寸也已统一更新为1366×768像素：
- ⚡ **电气拓扑** - 电气系统拓扑图
- 💧 **水冷拓扑** - 水冷系统拓扑图  
- 🔌 **I/O状态** - 输入输出状态监控
- 🔧 **单元状态** - 功能单元状态监控

## 🎨 技术特点

### 统一设计
- 所有弹窗采用相同的1366×768像素尺寸
- 一致的动画效果和交互方式
- 统一的关闭按钮和遮罩层样式

### 响应式适配
- 在小屏幕设备上自动调整为95vw × 80vh
- 保持良好的视觉效果和可用性

### 资源管理
- iframe内容在关闭时自动清理
- 避免内存泄漏和性能问题

## 🛠️ 自定义配置

### 修改弹窗尺寸
如需调整所有弹窗的尺寸，只需修改CSS变量：

```css
/* 在 styles.css 中修改 */
:root {
  --modal-width: 1920px;  /* 修改宽度 */
  --modal-height: 1080px; /* 修改高度 */
}
```

### 修改默认URL
在 `main.html` 的 `showModuleModal` 函数中修改：

```javascript
// 将百度改为其他网址
iframe.src = 'https://your-custom-url.com';
```

## 🧪 测试页面

提供了以下测试页面用于验证功能：

1. **debug-modal.html** - 弹窗功能调试页面
2. **menu-test.html** - 完整菜单流程测试
3. **test-modal.html** - 简化弹窗测试
4. **test-popup.html** - 功能概览测试

## ❓ 常见问题

### Q: 点击菜单项没有反应？
A: 请确保：
1. 先点击菜单按钮打开下拉菜单
2. 再点击具体的菜单项
3. 检查浏览器控制台是否有JavaScript错误

### Q: 弹窗显示不完整？
A: 请确保：
1. 浏览器窗口足够大（建议1920×1080以上）
2. 浏览器缩放比例为100%
3. 没有其他CSS样式冲突

### Q: iframe内容加载失败？
A: 可能原因：
1. 网络连接问题
2. 目标网站的跨域限制
3. 可以修改为其他支持iframe嵌入的网址

## 📞 技术支持

如遇到问题，请检查：
1. 浏览器控制台的错误信息
2. 网络连接状态
3. 文件路径是否正确

所有功能已完成实现并经过测试验证！
