<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>弹窗功能测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: #0a0e1a;
            color: white;
            padding: 20px;
        }
        .test-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .test-btn {
            padding: 15px 20px;
            background: linear-gradient(135deg, #00d4ff, #0099cc);
            border: none;
            border-radius: 8px;
            color: white;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0, 212, 255, 0.4);
        }
        .info {
            background: rgba(26, 31, 46, 0.8);
            padding: 20px;
            border-radius: 10px;
            border: 1px solid rgba(0, 212, 255, 0.3);
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>Unity WebGL 弹窗功能测试</h1>
    
    <div class="info">
        <h3>功能说明：</h3>
        <ul>
            <li>✅ 为5个下拉菜单项目实现了弹窗功能</li>
            <li>✅ 所有弹窗统一尺寸：1366×768像素</li>
            <li>✅ 使用iframe方式嵌入外部链接</li>
            <li>✅ 默认加载URL：http://www.baidu.com</li>
            <li>✅ 通过CSS变量统一管理弹窗尺寸</li>
        </ul>
    </div>

    <div class="test-buttons">
        <button class="test-btn" onclick="testRealtimeCurve()">
            📈 实时曲线
        </button>
        <button class="test-btn" onclick="testHistoryRecord()">
            📊 历史记录
        </button>
        <button class="test-btn" onclick="testHistoryEvent()">
            📅 历史事件
        </button>
        <button class="test-btn" onclick="testFaultWave()">
            🌊 故障录波
        </button>
        <button class="test-btn" onclick="testVersionInfo()">
            ℹ️ 版本信息
        </button>
    </div>

    <div class="info">
        <h3>现有弹窗（已更新尺寸）：</h3>
        <div class="test-buttons">
            <button class="test-btn" onclick="testElectricalTopology()">
                ⚡ 电气拓扑
            </button>
            <button class="test-btn" onclick="testCoolingTopology()">
                💧 水冷拓扑
            </button>
            <button class="test-btn" onclick="testIOStatus()">
                🔌 I/O状态
            </button>
            <button class="test-btn" onclick="testUnitStatus()">
                🔧 单元状态
            </button>
        </div>
    </div>

    <script>
        // 新增的5个模块弹窗测试函数
        function testRealtimeCurve() {
            alert('测试实时曲线弹窗\n在实际页面中会打开1366×768的弹窗，加载百度页面');
        }

        function testHistoryRecord() {
            alert('测试历史记录弹窗\n在实际页面中会打开1366×768的弹窗，加载百度页面');
        }

        function testHistoryEvent() {
            alert('测试历史事件弹窗\n在实际页面中会打开1366×768的弹窗，加载百度页面');
        }

        function testFaultWave() {
            alert('测试故障录波弹窗\n在实际页面中会打开1366×768的弹窗，加载百度页面');
        }

        function testVersionInfo() {
            alert('测试版本信息弹窗\n在实际页面中会打开1366×768的弹窗，加载百度页面');
        }

        // 现有弹窗测试函数
        function testElectricalTopology() {
            alert('测试电气拓扑弹窗\n尺寸已更新为1366×768');
        }

        function testCoolingTopology() {
            alert('测试水冷拓扑弹窗\n尺寸已更新为1366×768');
        }

        function testIOStatus() {
            alert('测试I/O状态弹窗\n尺寸已更新为1366×768');
        }

        function testUnitStatus() {
            alert('测试单元状态弹窗\n尺寸已更新为1366×768');
        }
    </script>
</body>
</html>
