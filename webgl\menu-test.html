<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>菜单功能测试</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div style="padding: 20px;">
        <h1>菜单功能测试</h1>
        
        <!-- 模拟主菜单结构 -->
        <div class="header-center" style="position: relative; display: inline-block;">
            <div class="system-title-section">
                <button class="menu-btn" onclick="toggleMainMenu()" style="padding: 15px 30px; background: #00d4ff; color: white; border: none; border-radius: 8px; cursor: pointer;">
                    <i class="fas fa-bars"></i>
                    <span>菜单</span>
                </button>
            </div>

            <!-- 主菜单下拉框 -->
            <div class="main-menu-dropdown" id="mainMenuDropdown" style="display: none; position: absolute; top: 100%; left: 0; background: #1a1f2e; border: 1px solid #00d4ff; border-radius: 8px; min-width: 200px; z-index: 1000;">
                <div class="menu-item" onclick="navigateToModule('realtime-curve')" style="padding: 15px; cursor: pointer; border-bottom: 1px solid rgba(0,212,255,0.2);">
                    <i class="fas fa-chart-line"></i>
                    <span style="margin-left: 10px;">实时曲线</span>
                </div>
                <div class="menu-item" onclick="navigateToModule('history-record')" style="padding: 15px; cursor: pointer; border-bottom: 1px solid rgba(0,212,255,0.2);">
                    <i class="fas fa-history"></i>
                    <span style="margin-left: 10px;">历史记录</span>
                </div>
                <div class="menu-item" onclick="navigateToModule('history-event')" style="padding: 15px; cursor: pointer; border-bottom: 1px solid rgba(0,212,255,0.2);">
                    <i class="fas fa-calendar-alt"></i>
                    <span style="margin-left: 10px;">历史事件</span>
                </div>
                <div class="menu-item" onclick="navigateToModule('fault-wave')" style="padding: 15px; cursor: pointer; border-bottom: 1px solid rgba(0,212,255,0.2);">
                    <i class="fas fa-wave-square"></i>
                    <span style="margin-left: 10px;">故障录波</span>
                </div>
                <div class="menu-item" onclick="navigateToModule('version-info')" style="padding: 15px; cursor: pointer;">
                    <i class="fas fa-info-circle"></i>
                    <span style="margin-left: 10px;">版本信息</span>
                </div>
            </div>
        </div>
        
        <div style="margin-top: 30px; padding: 15px; background: rgba(255,255,255,0.1); border-radius: 8px;">
            <h3>使用说明：</h3>
            <ol>
                <li>点击"菜单"按钮打开下拉菜单</li>
                <li>点击任意菜单项测试弹窗功能</li>
                <li>弹窗应该会显示并加载百度页面</li>
            </ol>
        </div>
        
        <div id="debug-log" style="margin-top: 20px; padding: 15px; background: rgba(255,255,255,0.1); border-radius: 8px;">
            <h3>调试日志：</h3>
            <div id="log-content"></div>
        </div>
    </div>

    <!-- 模块弹窗 -->
    <div id="moduleModal" class="modal-overlay" style="display: none;">
        <div class="modal-container module-modal">
            <div class="modal-header">
                <h2 id="moduleModalTitle"><i class="fas fa-cube"></i>模块</h2>
                <button class="modal-close-btn" onclick="closeModuleModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-content">
                <div class="module-iframe-container">
                    <iframe id="moduleIframe" src="" frameborder="0" class="module-iframe"></iframe>
                </div>
            </div>
        </div>
    </div>

    <script>
        function log(message) {
            const logContent = document.getElementById('log-content');
            const time = new Date().toLocaleTimeString();
            logContent.innerHTML += `<div style="margin: 5px 0; color: #00d4ff;">[${time}] ${message}</div>`;
            console.log(message);
        }

        /**
         * 切换主菜单显示/隐藏
         */
        function toggleMainMenu() {
            log('点击菜单按钮');
            const dropdown = document.getElementById('mainMenuDropdown');
            
            if (dropdown.style.display === 'none' || dropdown.style.display === '') {
                dropdown.style.display = 'block';
                log('显示下拉菜单');
            } else {
                dropdown.style.display = 'none';
                log('隐藏下拉菜单');
            }
        }

        /**
         * 导航到指定模块
         * @param {string} module - 模块名称
         */
        function navigateToModule(module) {
            log(`点击菜单项: ${module}`);
            
            const dropdown = document.getElementById('mainMenuDropdown');
            dropdown.style.display = 'none';
            log('隐藏下拉菜单');

            // 根据模块类型显示对应的弹窗
            switch(module) {
                case 'realtime-curve':
                    showModuleModal('realtime-curve', '实时曲线', 'fas fa-chart-line');
                    break;
                case 'history-record':
                    showModuleModal('history-record', '历史记录', 'fas fa-history');
                    break;
                case 'history-event':
                    showModuleModal('history-event', '历史事件', 'fas fa-calendar-alt');
                    break;
                case 'fault-wave':
                    showModuleModal('fault-wave', '故障录波', 'fas fa-wave-square');
                    break;
                case 'version-info':
                    showModuleModal('version-info', '版本信息', 'fas fa-info-circle');
                    break;
                default:
                    log(`未知模块: ${module}`);
            }
        }

        /**
         * 显示模块弹窗
         */
        function showModuleModal(moduleId, title, iconClass) {
            log(`尝试显示弹窗: ${title}`);
            
            const modal = document.getElementById('moduleModal');
            const modalTitle = document.getElementById('moduleModalTitle');
            const iframe = document.getElementById('moduleIframe');
            
            log(`元素查找: modal=${!!modal}, title=${!!modalTitle}, iframe=${!!iframe}`);
            
            if (modal && modalTitle && iframe) {
                // 设置标题和图标
                modalTitle.innerHTML = `<i class="${iconClass}"></i>${title}`;
                log(`设置标题: ${title}`);
                
                // 设置iframe源地址
                iframe.src = 'http://www.baidu.com';
                log('设置iframe地址为百度');
                
                // 显示弹窗
                modal.style.display = 'flex';
                setTimeout(() => {
                    modal.classList.add('show');
                    log('弹窗显示动画开始');
                }, 10);
                
                log(`${title}弹窗显示成功`);
            } else {
                log('错误: 找不到弹窗元素');
            }
        }

        /**
         * 关闭模块弹窗
         */
        function closeModuleModal() {
            log('关闭弹窗');
            
            const modal = document.getElementById('moduleModal');
            const iframe = document.getElementById('moduleIframe');
            
            if (modal) {
                modal.classList.remove('show');
                setTimeout(() => {
                    modal.style.display = 'none';
                    if (iframe) {
                        iframe.src = '';
                    }
                    log('弹窗已关闭');
                }, 300);
            }
        }

        // 点击其他地方关闭菜单
        document.addEventListener('click', function(event) {
            const dropdown = document.getElementById('mainMenuDropdown');
            const menuBtn = event.target.closest('.menu-btn');
            
            if (!menuBtn && !event.target.closest('.main-menu-dropdown')) {
                dropdown.style.display = 'none';
            }
        });

        // 点击遮罩层关闭弹窗
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('modal-overlay')) {
                closeModuleModal();
            }
        });

        // 页面加载完成
        window.addEventListener('load', function() {
            log('页面加载完成，功能已就绪');
        });
    </script>
</body>
</html>
