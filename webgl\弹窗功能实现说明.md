# Unity WebGL 界面弹窗功能实现说明

## 实现概述

为Unity WebGL界面中的5个下拉菜单项目成功实现了弹窗功能，并统一了所有弹窗的尺寸规范。

## 新增功能

### 1. 新增弹窗菜单项
- ✅ **实时曲线** - 显示实时数据曲线图表
- ✅ **历史记录** - 查看历史数据记录
- ✅ **历史事件** - 浏览历史事件日志
- ✅ **故障录波** - 故障波形分析工具
- ✅ **版本信息** - 系统版本详细信息

### 2. 弹窗规格统一
- **新增弹窗尺寸**: 1366×768像素
- **现有弹窗更新**: 电气拓扑、水冷拓扑、I/O状态、单元状态均更新为1366×768像素
- **默认加载URL**: http://www.baidu.com
- **显示方式**: iframe嵌入外部链接

## 技术实现详情

### 1. HTML结构修改

#### 新增模块弹窗HTML
```html
<!-- 模块弹窗 -->
<div id="moduleModal" class="modal-overlay" style="display: none;">
  <div class="modal-container module-modal">
    <div class="modal-header">
      <h2 id="moduleModalTitle"><i class="fas fa-cube"></i>模块</h2>
      <button class="modal-close-btn" onclick="closeModuleModal()">
        <i class="fas fa-times"></i>
      </button>
    </div>
    <div class="modal-content">
      <div class="module-iframe-container">
        <iframe id="moduleIframe" src="" frameborder="0" class="module-iframe"></iframe>
      </div>
    </div>
  </div>
</div>
```

### 2. JavaScript功能实现

#### 修改导航函数
```javascript
function navigateToModule(module) {
  const dropdown = document.getElementById('mainMenuDropdown');
  dropdown.classList.remove('show');
  
  // 根据模块类型显示对应的弹窗
  switch(module) {
    case 'realtime-curve':
      showModuleModal('realtime-curve', '实时曲线', 'fas fa-chart-line');
      break;
    case 'history-record':
      showModuleModal('history-record', '历史记录', 'fas fa-history');
      break;
    // ... 其他模块
  }
}
```

#### 新增弹窗控制函数
```javascript
/**
 * 显示模块弹窗
 * @param {string} moduleId - 模块ID
 * @param {string} title - 弹窗标题
 * @param {string} iconClass - 图标类名
 */
function showModuleModal(moduleId, title, iconClass) {
  const modal = document.getElementById('moduleModal');
  const modalTitle = document.getElementById('moduleModalTitle');
  const iframe = document.getElementById('moduleIframe');
  
  if (modal && modalTitle && iframe) {
    // 设置标题和图标
    modalTitle.innerHTML = `<i class="${iconClass}"></i>${title}`;
    
    // 设置iframe源地址 - 默认加载百度
    iframe.src = 'http://www.baidu.com';
    
    // 显示弹窗
    modal.style.display = 'flex';
    setTimeout(() => {
      modal.classList.add('show');
    }, 10);
  }
}

/**
 * 关闭模块弹窗
 */
function closeModuleModal() {
  const modal = document.getElementById('moduleModal');
  const iframe = document.getElementById('moduleIframe');
  
  if (modal) {
    modal.classList.remove('show');
    setTimeout(() => {
      modal.style.display = 'none';
      // 清理iframe内容以释放资源
      if (iframe) {
        iframe.src = '';
      }
    }, 300);
  }
}
```

### 3. CSS样式实现

#### 新增CSS变量
```css
:root {
  /* 弹窗尺寸 */
  --modal-width: 1366px;
  --modal-height: 768px;
}
```

#### 模块弹窗样式
```css
/* 模块弹窗特定样式 */
.module-modal {
  width: var(--modal-width);
  height: var(--modal-height);
}

/* 模块iframe容器 */
.module-iframe-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: 10px;
  overflow: hidden;
}

/* 模块iframe */
.module-iframe {
  width: 100%;
  height: 100%;
  border: none;
  border-radius: 10px;
  background: #fff;
}
```

#### 现有弹窗尺寸更新
```css
/* 所有弹窗统一使用CSS变量 */
.io-status-modal,
.topology-modal,
.unit-status-modal,
.module-modal {
  width: var(--modal-width);
  height: var(--modal-height);
}
```

## 功能特点

### 1. 统一的用户体验
- 所有弹窗采用相同的尺寸规格
- 一致的动画效果和交互方式
- 统一的关闭按钮和遮罩层

### 2. 响应式设计
- 在小屏幕设备上自动调整尺寸
- 保持良好的视觉效果和可用性

### 3. 资源管理
- iframe内容在关闭时自动清理
- 避免内存泄漏和性能问题

### 4. 易于维护
- 通过CSS变量统一管理弹窗尺寸
- 模块化的JavaScript函数设计
- 清晰的代码结构和注释

## 使用方法

### 1. 修改弹窗尺寸
只需修改CSS变量即可统一调整所有弹窗尺寸：
```css
:root {
  --modal-width: 1920px;  /* 修改宽度 */
  --modal-height: 1080px; /* 修改高度 */
}
```

### 2. 修改默认URL
在`showModuleModal`函数中修改iframe的src属性：
```javascript
iframe.src = 'https://your-custom-url.com';
```

### 3. 添加新的模块弹窗
1. 在`navigateToModule`函数的switch语句中添加新的case
2. 调用`showModuleModal`函数并传入相应参数

## 测试验证

创建了测试页面 `test-popup.html` 用于验证功能实现：
- 可以测试所有5个新增弹窗功能
- 验证现有弹窗尺寸更新
- 确认响应式设计效果

## 总结

本次实现完全满足了用户的需求：
- ✅ 为5个菜单项实现了弹窗功能
- ✅ 统一了所有弹窗尺寸为1366×768像素
- ✅ 使用iframe方式加载外部链接
- ✅ 默认加载百度页面
- ✅ 通过CSS变量便于后续调整
- ✅ 保持了与现有界面的一致性

所有功能已经实现并可以正常使用。用户可以通过点击下拉菜单中的对应项目来打开相应的弹窗。
