<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>弹窗功能测试</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div style="padding: 20px; text-align: center;">
        <h1>弹窗功能测试</h1>
        <div style="margin: 20px 0;">
            <button onclick="testRealtimeCurve()" style="margin: 10px; padding: 15px 30px; background: #00d4ff; color: white; border: none; border-radius: 8px; cursor: pointer;">
                <i class="fas fa-chart-line"></i> 实时曲线
            </button>
            <button onclick="testHistoryRecord()" style="margin: 10px; padding: 15px 30px; background: #00d4ff; color: white; border: none; border-radius: 8px; cursor: pointer;">
                <i class="fas fa-history"></i> 历史记录
            </button>
            <button onclick="testHistoryEvent()" style="margin: 10px; padding: 15px 30px; background: #00d4ff; color: white; border: none; border-radius: 8px; cursor: pointer;">
                <i class="fas fa-calendar-alt"></i> 历史事件
            </button>
            <button onclick="testFaultWave()" style="margin: 10px; padding: 15px 30px; background: #00d4ff; color: white; border: none; border-radius: 8px; cursor: pointer;">
                <i class="fas fa-wave-square"></i> 故障录波
            </button>
            <button onclick="testVersionInfo()" style="margin: 10px; padding: 15px 30px; background: #00d4ff; color: white; border: none; border-radius: 8px; cursor: pointer;">
                <i class="fas fa-info-circle"></i> 版本信息
            </button>
        </div>
    </div>

    <!-- 模块弹窗 -->
    <div id="moduleModal" class="modal-overlay" style="display: none;">
      <div class="modal-container module-modal">
        <div class="modal-header">
          <h2 id="moduleModalTitle"><i class="fas fa-cube"></i>模块</h2>
          <button class="modal-close-btn" onclick="closeModuleModal()">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-content">
          <div class="module-iframe-container">
            <iframe id="moduleIframe" src="" frameborder="0" class="module-iframe"></iframe>
          </div>
        </div>
      </div>
    </div>

    <script>
        /**
         * 显示模块弹窗
         * @param {string} moduleId - 模块ID
         * @param {string} title - 弹窗标题
         * @param {string} iconClass - 图标类名
         */
        function showModuleModal(moduleId, title, iconClass) {
            const modal = document.getElementById('moduleModal');
            const modalTitle = document.getElementById('moduleModalTitle');
            const iframe = document.getElementById('moduleIframe');
            
            console.log('尝试显示弹窗:', { moduleId, title, iconClass });
            console.log('元素查找结果:', {
                modal: !!modal,
                modalTitle: !!modalTitle,
                iframe: !!iframe
            });
            
            if (modal && modalTitle && iframe) {
                // 设置标题和图标
                modalTitle.innerHTML = `<i class="${iconClass}"></i>${title}`;
                
                // 设置iframe源地址 - 默认加载百度
                iframe.src = 'http://www.baidu.com';
                
                // 显示弹窗
                modal.style.display = 'flex';
                setTimeout(() => {
                    modal.classList.add('show');
                }, 10);
                
                console.log(`显示${title}模块弹窗成功`);
            } else {
                console.error('模块弹窗元素未找到:', {
                    modal: !!modal,
                    modalTitle: !!modalTitle,
                    iframe: !!iframe
                });
            }
        }

        /**
         * 关闭模块弹窗
         */
        function closeModuleModal() {
            const modal = document.getElementById('moduleModal');
            const iframe = document.getElementById('moduleIframe');
            
            if (modal) {
                modal.classList.remove('show');
                setTimeout(() => {
                    modal.style.display = 'none';
                    // 清理iframe内容以释放资源
                    if (iframe) {
                        iframe.src = '';
                    }
                }, 300);
                console.log('关闭模块弹窗');
            }
        }

        // 测试函数
        function testRealtimeCurve() {
            showModuleModal('realtime-curve', '实时曲线', 'fas fa-chart-line');
        }

        function testHistoryRecord() {
            showModuleModal('history-record', '历史记录', 'fas fa-history');
        }

        function testHistoryEvent() {
            showModuleModal('history-event', '历史事件', 'fas fa-calendar-alt');
        }

        function testFaultWave() {
            showModuleModal('fault-wave', '故障录波', 'fas fa-wave-square');
        }

        function testVersionInfo() {
            showModuleModal('version-info', '版本信息', 'fas fa-info-circle');
        }

        // 点击遮罩层关闭弹窗
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('modal-overlay')) {
                closeModuleModal();
            }
        });
    </script>
</body>
</html>
