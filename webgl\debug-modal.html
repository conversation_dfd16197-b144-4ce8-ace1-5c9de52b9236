<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>弹窗调试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: #0a0e1a;
            color: white;
            padding: 20px;
        }
        
        .test-btn {
            padding: 15px 30px;
            background: #00d4ff;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px;
            font-size: 16px;
        }
        
        .test-btn:hover {
            background: #0099cc;
        }
        
        /* 弹窗样式 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .modal-overlay.show {
            opacity: 1;
        }
        
        .modal-container {
            background: linear-gradient(135deg, #1a1f2e 0%, #2a3142 100%);
            border: 2px solid rgba(0, 212, 255, 0.3);
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 212, 255, 0.2);
            overflow: hidden;
            transform: scale(0.9);
            transition: transform 0.3s ease;
        }
        
        .modal-overlay.show .modal-container {
            transform: scale(1);
        }
        
        .module-modal {
            width: 1366px;
            height: 768px;
        }
        
        .modal-header {
            background: linear-gradient(135deg, #00d4ff, #0099cc);
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .modal-header h2 {
            margin: 0;
            color: white;
            font-size: 18px;
        }
        
        .modal-close-btn {
            background: none;
            border: none;
            color: white;
            font-size: 20px;
            cursor: pointer;
            padding: 5px;
            border-radius: 4px;
        }
        
        .modal-close-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }
        
        .modal-content {
            padding: 0;
            height: calc(100% - 60px);
        }
        
        .module-iframe-container {
            width: 100%;
            height: 100%;
            background: white;
        }
        
        .module-iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        
        @media (max-width: 1400px) {
            .module-modal {
                width: 95vw;
                height: 80vh;
            }
        }
    </style>
</head>
<body>
    <h1>弹窗功能调试</h1>
    <p>点击下面的按钮测试弹窗功能：</p>
    
    <div>
        <button class="test-btn" onclick="testModal('实时曲线', 'fas fa-chart-line')">
            📈 实时曲线
        </button>
        <button class="test-btn" onclick="testModal('历史记录', 'fas fa-history')">
            📊 历史记录
        </button>
        <button class="test-btn" onclick="testModal('历史事件', 'fas fa-calendar-alt')">
            📅 历史事件
        </button>
        <button class="test-btn" onclick="testModal('故障录波', 'fas fa-wave-square')">
            🌊 故障录波
        </button>
        <button class="test-btn" onclick="testModal('版本信息', 'fas fa-info-circle')">
            ℹ️ 版本信息
        </button>
    </div>
    
    <div id="debug-info" style="margin-top: 20px; padding: 15px; background: rgba(255,255,255,0.1); border-radius: 8px;">
        <h3>调试信息：</h3>
        <div id="debug-log"></div>
    </div>

    <!-- 模块弹窗 -->
    <div id="moduleModal" class="modal-overlay" style="display: none;">
        <div class="modal-container module-modal">
            <div class="modal-header">
                <h2 id="moduleModalTitle">
                    <i class="fas fa-cube"></i> 模块
                </h2>
                <button class="modal-close-btn" onclick="closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-content">
                <div class="module-iframe-container">
                    <iframe id="moduleIframe" src="" class="module-iframe"></iframe>
                </div>
            </div>
        </div>
    </div>

    <script>
        function log(message) {
            const debugLog = document.getElementById('debug-log');
            const time = new Date().toLocaleTimeString();
            debugLog.innerHTML += `<div>[${time}] ${message}</div>`;
            console.log(message);
        }
        
        function testModal(title, iconClass) {
            log(`尝试打开弹窗: ${title}`);
            
            const modal = document.getElementById('moduleModal');
            const modalTitle = document.getElementById('moduleModalTitle');
            const iframe = document.getElementById('moduleIframe');
            
            log(`元素查找结果: modal=${!!modal}, title=${!!modalTitle}, iframe=${!!iframe}`);
            
            if (modal && modalTitle && iframe) {
                // 设置标题
                modalTitle.innerHTML = `<i class="${iconClass}"></i> ${title}`;
                log(`设置标题: ${title}`);
                
                // 设置iframe
                iframe.src = 'http://www.baidu.com';
                log('设置iframe源地址');
                
                // 显示弹窗
                modal.style.display = 'flex';
                log('设置display为flex');
                
                setTimeout(() => {
                    modal.classList.add('show');
                    log('添加show类');
                }, 10);
                
                log(`弹窗显示成功: ${title}`);
            } else {
                log('错误: 找不到必要的DOM元素');
            }
        }
        
        function closeModal() {
            log('尝试关闭弹窗');
            
            const modal = document.getElementById('moduleModal');
            const iframe = document.getElementById('moduleIframe');
            
            if (modal) {
                modal.classList.remove('show');
                log('移除show类');
                
                setTimeout(() => {
                    modal.style.display = 'none';
                    if (iframe) {
                        iframe.src = '';
                    }
                    log('弹窗已隐藏');
                }, 300);
            }
        }
        
        // 点击遮罩层关闭
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('modal-overlay')) {
                closeModal();
            }
        });
        
        // 页面加载完成后的初始化
        window.addEventListener('load', function() {
            log('页面加载完成');
            log(`找到的元素: moduleModal=${!!document.getElementById('moduleModal')}`);
        });
    </script>
</body>
</html>
